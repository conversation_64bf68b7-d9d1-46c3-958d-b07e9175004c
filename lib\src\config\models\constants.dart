import 'package:flutter/material.dart';
import '../colors.dart';

class Constants {
  static const TextStyle titleStyle = TextStyle(
    fontSize: 32,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
    fontFamily: 'Cairo',
    height: 1.2,
  );

  static const TextStyle subtitleStyle = TextStyle(
    fontSize: 18,
    color: AppColors.textSecondary,
    fontFamily: 'Cairo',
    height: 1.5,
  );

  static const TextStyle sectionTitleStyle = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
    fontFamily: 'Cairo',
    height: 1.3,
  );

  static const TextStyle buttonTextStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.bold,
    color: AppColors.textLight,
    fontFamily: 'Cairo',
    letterSpacing: 0.5,
  );

  static const EdgeInsets screenPadding = EdgeInsets.all(24.0);
  static const EdgeInsets cardPadding = EdgeInsets.all(16.0);
  static const double smallSpacing = 8.0;
  static const double mediumSpacing = 16.0;
  static const double largeSpacing = 24.0;
  static const double extraLargeSpacing = 32.0;

  static final BorderRadius borderRadius = BorderRadius.circular(12);
  static const double borderWidth = 1.5;

  static OutlineInputBorder defaultBorder = OutlineInputBorder(
    borderRadius: borderRadius,
    borderSide: const BorderSide(
      color: AppColors.borderPrimary,
      width: borderWidth,
    ),
  );

  static OutlineInputBorder focusedBorder = OutlineInputBorder(
    borderRadius: borderRadius,
    borderSide: const BorderSide(
      color: AppColors.borderFocused,
      width: borderWidth * 2,
    ),
  );

  static final ButtonStyle primaryButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: AppColors.buttonPrimary,
    foregroundColor: AppColors.textLight,
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
    shape: RoundedRectangleBorder(borderRadius: borderRadius),
    elevation: 2,
  );

  static final ButtonStyle secondaryButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: AppColors.buttonSecondary,
    foregroundColor: AppColors.textLight,
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
    shape: RoundedRectangleBorder(borderRadius: borderRadius),
    elevation: 2,
  );

  static final CardTheme cardTheme = CardTheme(
    color: AppColors.backgroundPrimary,
    elevation: 2,
    shape: RoundedRectangleBorder(borderRadius: borderRadius),
    shadowColor: AppColors.shadowColor,
  );

  static final InputDecorationTheme inputDecorationTheme = InputDecorationTheme(
    filled: true,
    fillColor: AppColors.backgroundPrimary,
    border: defaultBorder,
    enabledBorder: defaultBorder,
    focusedBorder: focusedBorder,
    errorBorder: defaultBorder.copyWith(
      borderSide: const BorderSide(color: Colors.red, width: borderWidth),
    ),
    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
  );
}

